import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Shopify API configuration
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
const productsEndpoint = shopifyEndpoint.replace("graphql.json", "products.json");

// Rate limiting
const RATE_LIMIT_DELAY = 500; // 500ms between requests

/**
 * Sleep function for rate limiting
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Find Shopify product by SKU
 * @param {string} sku - The SKU to search for
 * @returns {Object|null} - Product data or null if not found
 */
async function findProductBySku(sku) {
  try {
    console.log(`🔍 Searching for product with SKU: ${sku}`);
    
    const searchUrl = `${productsEndpoint}?limit=250&fields=id,title,tags,variants`;
    
    const response = await fetch(searchUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      }
    });

    if (!response.ok) {
      throw new Error(`Shopify API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Search through all products and their variants for the SKU
    for (const product of data.products) {
      for (const variant of product.variants) {
        if (variant.sku === sku) {
          console.log(`✅ Found product ${product.id} with SKU ${sku}`);
          return {
            productId: product.id,
            variantId: variant.id,
            title: product.title,
            currentTags: product.tags ? product.tags.split(',').map(tag => tag.trim()) : []
          };
        }
      }
    }
    
    console.log(`❌ No product found with SKU: ${sku}`);
    return null;
    
  } catch (error) {
    console.error(`❌ Error searching for product with SKU ${sku}:`, error.message);
    throw error;
  }
}

/**
 * Update Shopify product tags
 * @param {string} productId - The product ID
 * @param {Array} newTags - Array of new tags
 * @returns {Object} - Update result
 */
async function updateProductTags(productId, newTags) {
  try {
    console.log(`🔄 Updating product ${productId} with tags: ${newTags.join(', ')}`);
    
    const updateEndpoint = `${productsEndpoint.replace('.json', '')}/${productId}.json`;
    
    const payload = {
      product: {
        id: productId,
        tags: newTags.join(',')
      }
    };

    const response = await fetch(updateEndpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const updatedProduct = await response.json();
    console.log(`✅ Successfully updated product ${productId} tags`);
    
    return {
      success: true,
      product: updatedProduct.product
    };
    
  } catch (error) {
    console.error(`❌ Error updating product ${productId}:`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Remove existing weight range tags from tag array
 * @param {Array} tags - Current tags array
 * @returns {Array} - Tags with weight range tags removed
 */
function removeExistingWeightRangeTags(tags) {
  const weightRangePattern = /^wt_rng_\d+-\d+$/;
  return tags.filter(tag => !weightRangePattern.test(tag));
}

/**
 * Process a fix_weight_range task
 * @param {Object} task - The task object
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
async function processFixWeightRangeTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processFixWeightRangeTask] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      if (typeof task.payload === 'object' && task.payload !== null) {
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        payload = JSON.parse(task.payload);
      } else {
        throw new Error('Invalid payload format');
      }
    } catch (err) {
      const errMsg = `[processFixWeightRangeTask] Error parsing task payload: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, 'Parsing task payload');
      await updateTaskStatus(task.id, 'failed', { error: errMsg });
      return;
    }

    const { disc_id, weight, expected_weight_range_tag } = payload;
    
    if (!disc_id || !weight || !expected_weight_range_tag) {
      const errMsg = `[processFixWeightRangeTask] Missing required payload fields: disc_id=${disc_id}, weight=${weight}, expected_weight_range_tag=${expected_weight_range_tag}`;
      console.error(errMsg);
      await updateTaskStatus(task.id, 'failed', { error: errMsg });
      return;
    }

    console.log(`[processFixWeightRangeTask] Processing disc ${disc_id} with weight ${weight}g, expected tag: ${expected_weight_range_tag}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Generate SKU for the disc (format: D{disc_id})
    const sku = `D${disc_id}`;
    console.log(`[processFixWeightRangeTask] Looking for Shopify product with SKU: ${sku}`);

    // Find the product in Shopify
    const shopifyProduct = await findProductBySku(sku);
    await sleep(RATE_LIMIT_DELAY);

    if (!shopifyProduct) {
      const errMsg = `Product not found in Shopify for SKU: ${sku}`;
      console.log(`[processFixWeightRangeTask] ${errMsg}`);
      await updateTaskStatus(task.id, 'failed', {
        error: errMsg,
        disc_id: disc_id,
        sku: sku
      });
      return;
    }

    // Check if the expected weight range tag is already present
    const currentTags = shopifyProduct.currentTags;
    const hasExpectedTag = currentTags.includes(expected_weight_range_tag);
    
    if (hasExpectedTag) {
      console.log(`[processFixWeightRangeTask] Product ${shopifyProduct.productId} already has the correct weight range tag: ${expected_weight_range_tag}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Product already has correct weight range tag: ${expected_weight_range_tag}`,
        disc_id: disc_id,
        sku: sku,
        product_id: shopifyProduct.productId,
        weight_range_tag: expected_weight_range_tag,
        action: 'no_change_needed'
      });
      return;
    }

    // Remove any existing weight range tags and add the new one
    const tagsWithoutWeightRange = removeExistingWeightRangeTags(currentTags);
    const newTags = [...tagsWithoutWeightRange, expected_weight_range_tag];
    
    console.log(`[processFixWeightRangeTask] Updating tags from [${currentTags.join(', ')}] to [${newTags.join(', ')}]`);

    // Update the product tags
    const updateResult = await updateProductTags(shopifyProduct.productId, newTags);
    await sleep(RATE_LIMIT_DELAY);

    if (updateResult.success) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated weight range tag to: ${expected_weight_range_tag}`,
        disc_id: disc_id,
        sku: sku,
        product_id: shopifyProduct.productId,
        weight_range_tag: expected_weight_range_tag,
        old_tags: currentTags,
        new_tags: newTags,
        action: 'updated'
      });
      console.log(`[processFixWeightRangeTask] Successfully completed weight range update for disc ${disc_id}`);
    } else {
      const errMsg = `Failed to update Shopify product tags: ${updateResult.error}`;
      console.error(`[processFixWeightRangeTask] ${errMsg}`);
      await updateTaskStatus(task.id, 'failed', {
        error: errMsg,
        disc_id: disc_id,
        sku: sku,
        product_id: shopifyProduct.productId
      });
    }

  } catch (err) {
    const errMsg = `[processFixWeightRangeTask] Exception processing task: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, 'Processing fix_weight_range task');
    await updateTaskStatus(task.id, 'failed', { error: errMsg });
  }
}

export { processFixWeightRangeTask };
