// publishProductOSL.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

const args = minimist(process.argv.slice(2));
const oslId = args.id;
if (!oslId) {
  console.error('No order sheet line id provided. Use --id=<orderSheetLineId>');
  process.exit(1);
}
console.log(`INFO: Received order sheet line id: ${oslId}`);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error("ERROR: Missing Supabase URL or key in environment variables.");
  process.exit(1);
}
console.log("INFO: Initializing Supabase client...");
const supabase = createClient(supabaseUrl, supabaseKey);

// Helper: log errors to t_error_logs
async function logError(errorMessage, context) {
  try {
    const { error } = await supabase
      .from("t_error_logs")
      .insert({ error_message: errorMessage, context });
    if (error) console.error("Failed to log error:", error);
  } catch (err) {
    console.error("Exception while logging error:", err);
  }
}

// Derive Shopify credentials from environment variables
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT; // e.g., https://yourstore.myshopify.com/admin/api/2023-01/graphql.json
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error("ERROR: Missing Shopify endpoint or access token.");
  process.exit(1);
}

// REST product endpoint used for creation/add variant
// e.g. https://yourstore.myshopify.com/admin/api/2023-01/products.json
const productsEndpoint = shopifyEndpoint.replace("graphql.json", "products.json");
console.log(`INFO: Shopify products endpoint: ${productsEndpoint}`);

// A small helper function to call Shopify's GraphQL Admin API.
async function shopifyGraphQLRequest(query, variables = {}) {
  console.log("DEBUG: GraphQL endpoint:", shopifyEndpoint);
  console.log("DEBUG: GraphQL query:", query);
  console.log("DEBUG: GraphQL variables:", JSON.stringify(variables, null, 2));

  const response = await fetch(shopifyEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify({ query, variables })
  });

  const body = await response.json();

  console.log("DEBUG: GraphQL response status:", response.status);
  console.log("DEBUG: GraphQL response body:", JSON.stringify(body, null, 2));

  if (!response.ok || body.errors) {
    throw new Error(`GraphQL error: ${JSON.stringify(body.errors || body.data)}`);
  }
  return body.data;
}

/**
 * Check if a product exists on Shopify by handle using GraphQL.
 * Returns the product GID (e.g., "gid://shopify/Product/123456789") if found, otherwise null.
 */
async function getProductGIDByHandle(handle) {
  const query = `
    query getProductByHandle($handle: String!) {
      productByHandle(handle: $handle) {
        id
        handle
      }
    }
  `;
  const variables = { handle };
  const data = await shopifyGraphQLRequest(query, variables);

  console.log("DEBUG: GraphQL productByHandle result (data):", JSON.stringify(data, null, 2));

  if (data.productByHandle) {
    console.log("DEBUG: Found productByHandle with ID:", data.productByHandle.id, "and handle:", data.productByHandle.handle);
    return data.productByHandle.id; // GID format
  }
  console.log("DEBUG: productByHandle returned null (no matching product).");
  return null;
}

/**
 * Get detailed product information including options structure using REST API.
 * Returns the product details with options if found, otherwise null.
 */
async function getProductDetailsById(productId) {
  const productBase = productsEndpoint.split('/products.json')[0];
  const singleProductUrl = `${productBase}/products/${productId}.json`;

  console.log(`DEBUG: Fetching product details from: ${singleProductUrl}`);

  const response = await fetch(singleProductUrl, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    }
  });

  const result = await response.json();
  console.log("DEBUG: Get product details response status:", response.status);
  console.log("DEBUG: Get product details response body:", JSON.stringify(result, null, 2));

  if (!response.ok) {
    throw new Error(`Error fetching product ${productId}: ${JSON.stringify(result)}`);
  }

  return result.product;
}

/**
 * Creates a product on Shopify using the REST Admin API.
 */
async function createShopifyProduct(productData) {
  const payload = {
    product: {
      title: productData.title,
      handle: productData.handle,
      body_html: "",
      vendor: productData.vendor,
      product_type: "Disc",
      tags: productData.tags,
      status: "active",
      published: true,
      published_scope: "global",
      template_suffix: "os-discs-landing",
      options: [
        { name: "Weight Range", values: [ productData.option1Value ] },
        { name: "Mold",         values: [ productData.option2Value ] },
        { name: "Color",        values: [ productData.option3Value ] }
      ],
      variants: [
        {
          option1: productData.option1Value,
          option2: productData.option2Value,
          option3: productData.option3Value,
          sku: productData.variantSKU,
          barcode: productData.variantBarcode,
          weight: productData.variantWeight,
          weight_unit: "lb",
          price: productData.variantPrice,
          compare_at_price: productData.variantCompareAtPrice,
          taxable: true,
          inventory_management: "shopify",
          inventory_policy: "deny",
          fulfillment_service: "manual",
          requires_shipping: true,
          inventory_quantity: productData.variantInventoryQty
        }
      ],
      images: [
        { src: productData.imageSrc, alt: productData.imageAltText }
      ]
    }
  };

  console.log("INFO: Final product payload to Shopify:\n", JSON.stringify(payload, null, 2));

  const response = await fetch(productsEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify(payload)
  });

  const result = await response.json();
  console.log("DEBUG: Create product response status:", response.status);
  console.log("DEBUG: Create product response body:", JSON.stringify(result, null, 2));

  if (!response.ok) {
    throw new Error(
      `Error creating product for Order Sheet Line id "${productData.orderSheetLineId}": ` +
      JSON.stringify(result)
    );
  }

  if (result.product) {
    console.log("DEBUG: Created product handle:", result.product.handle);
  }
  return result.product;
}

/**
 * Adds a new variant to an existing Shopify product (REST).
 */
async function addVariantToProduct(productData, productId) {
  const variantPayload = {
    variant: {
      option1: productData.option1Value,
      option2: productData.option2Value,
      option3: productData.option3Value,
      sku: productData.variantSKU,
      barcode: productData.variantBarcode,
      weight: productData.variantWeight,
      weight_unit: "lb",
      price: productData.variantPrice,
      compare_at_price: productData.variantCompareAtPrice,
      taxable: true,
      inventory_management: "shopify",
      inventory_policy: "deny",
      fulfillment_service: "manual",
      requires_shipping: true,
      inventory_quantity: productData.variantInventoryQty
    }
  };

  console.log("INFO: Final variant payload to Shopify:\n", JSON.stringify(variantPayload, null, 2));

  // Derive base URL from productsEndpoint to handle variants
  const productBase = productsEndpoint.split('/products.json')[0];
  const variantUrl = `${productBase}/products/${productId}/variants.json`;

  const variantResponse = await fetch(variantUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify(variantPayload)
  });

  const variantResult = await variantResponse.json();
  console.log("DEBUG: Add variant response status:", variantResponse.status);
  console.log("DEBUG: Add variant response body:", JSON.stringify(variantResult, null, 2));

  if (!variantResponse.ok) {
    // Check if this is a "variant already exists" error
    if (variantResult.errors && variantResult.errors.base) {
      const errorMessages = variantResult.errors.base;
      const variantExistsError = errorMessages.find(msg =>
        msg.includes('already exists') && msg.includes('variant')
      );

      if (variantExistsError) {
        console.log(`INFO: Variant already exists - treating as success: ${variantExistsError}`);
        // Return a mock variant object to indicate success
        return {
          id: 'existing',
          sku: productData.variantSKU,
          option1: productData.option1Value,
          option2: productData.option2Value,
          option3: productData.option3Value,
          message: 'Variant already exists - no action needed'
        };
      }
    }

    // If it's not a "variant already exists" error, throw as before
    throw new Error(`Error adding variant: ${JSON.stringify(variantResult)}`);
  }
  return variantResult.variant;
}

/**
 * Associates an existing image with a newly created variant.
 */
async function updateVariantImage(variantId, imageId) {
  console.log(`INFO: Associating variant ${variantId} with image ${imageId}...`);

  const productBase = productsEndpoint.split('/products.json')[0];
  const variantUrl = `${productBase}/variants/${variantId}.json`;

  const body = {
    variant: {
      id: variantId,
      image_id: imageId
    }
  };

  const response = await fetch(variantUrl, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify(body)
  });

  const result = await response.json();
  console.log("DEBUG: Update variant image response status:", response.status);
  console.log("DEBUG: Update variant image response body:", JSON.stringify(result, null, 2));

  if (!response.ok) {
    throw new Error(`Error associating image with variant: ${JSON.stringify(result)}`);
  }

  console.log(`INFO: Successfully associated variant ${variantId} with image ${imageId}.`);
  return result.variant;
}

/**
 * Helper to delay execution.
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function main() {
  console.log("INFO: Waiting 5 seconds for v_todo_osls to update...");
  await delay(5000);

  // 1) Check if OSL is present in v_todo_osls
  console.log(`INFO: Checking if Order Sheet Line id ${oslId} is present in v_todo_osls...`);
  const { data: todoRecords, error: todoError } = await supabase
    .from("v_todo_osls")
    .select("id")
    .eq("id", oslId);
  if (todoError) {
    const msg = `Error checking v_todo_osls: ${JSON.stringify(todoError)}`;
    console.error("ERROR:", msg);
    await logError(msg, "v_todo_osls check");
    process.exit(1);
  }
  if (todoRecords && todoRecords.length > 0) {
    const noteMessage = `After delay: Order Sheet Line id ${oslId} is present in v_todo_osls and is NOT ready to be processed.`;
    console.log(`INFO: ${noteMessage}`);
    const { error: noteError } = await supabase
      .from("t_order_sheet_lines")
      .update({ shopify_product_uploaded_notes: noteMessage })
      .eq("id", oslId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_product_uploaded_notes for OSL id ${oslId}: ${noteError.message}`);
      await logError(noteError.message, `Updating shopify_product_uploaded_notes for OSL ${oslId}`);
    }
    process.exit(0);
  }

  // 2) Fetch the OSL record
  console.log(`INFO: Fetching Order Sheet Line record with id ${oslId} from t_order_sheet_lines...`);
  const { data: oslRecord, error: oslError } = await supabase
    .from("t_order_sheet_lines")
    .select("*")
    .eq("id", oslId)
    .single();
  if (oslError) {
    const msg = `Error fetching OSL: ${JSON.stringify(oslError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Fetching OSL ${oslId}`);
    process.exit(1);
  }

  // 3) Retrieve the related MPS record.
  console.log(`INFO: Fetching MPS record with id ${oslRecord.mps_id}...`);
  const { data: mpsRecord, error: mpsError } = await supabase
    .from("t_mps")
    .select("*")
    .eq("id", oslRecord.mps_id)
    .single();
  if (mpsError) {
    const msg = `Error fetching MPS: ${JSON.stringify(mpsError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Fetching MPS for OSL ${oslId}`);
    process.exit(1);
  }

  // 4) Fetch associated plastic, mold, stamp, and brand records.
  console.log(`INFO: Fetching plastic record for id ${mpsRecord.plastic_id}...`);
  const { data: plasticData, error: plasticError } = await supabase
    .from("t_plastics")
    .select("*")
    .eq("id", mpsRecord.plastic_id)
    .single();
  if (plasticError) {
    const msg = `Error fetching plastic: ${JSON.stringify(plasticError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Fetching plastic for OSL ${oslId}`);
    process.exit(1);
  }

  console.log(`INFO: Fetching mold record for id ${mpsRecord.mold_id}...`);
  const { data: moldData, error: moldError } = await supabase
    .from("t_molds")
    .select("*")
    .eq("id", mpsRecord.mold_id)
    .single();
  if (moldError) {
    const msg = `Error fetching mold: ${JSON.stringify(moldError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Fetching mold for OSL ${oslId}`);
    process.exit(1);
  }

  console.log(`INFO: Fetching stamp record for id ${mpsRecord.stamp_id}...`);
  const { data: stampData, error: stampError } = await supabase
    .from("t_stamps")
    .select("*")
    .eq("id", mpsRecord.stamp_id)
    .single();
  if (stampError) {
    const msg = `Error fetching stamp: ${JSON.stringify(stampError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Fetching stamp for OSL ${oslId}`);
    process.exit(1);
  }

  // Fetch player data from stamp if player_id exists
  let stampPlayerData = null;
  if (stampData.player_id) {
    console.log(`INFO: Fetching t_players record for stamp player_id=${stampData.player_id}`);
    const { data, error } = await supabase
      .from('t_players')
      .select('*')
      .eq('id', stampData.player_id)
      .single();
    if (error) {
      console.warn(`WARNING: Could not fetch t_players record for stamp: ${error.message}`);
    } else {
      stampPlayerData = data;
      console.log(`INFO: t_players record from stamp: ${JSON.stringify(stampPlayerData)}`);
    }
  } else {
    console.log("INFO: No player_id available from t_stamps.");
  }

  console.log(`INFO: Fetching brand record for id ${moldData.brand_id}...`);
  const { data: brandData, error: brandError } = await supabase
    .from("t_brands")
    .select("*")
    .eq("id", moldData.brand_id)
    .single();
  if (brandError) {
    const msg = `Error fetching brand: ${JSON.stringify(brandError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Fetching brand for OSL ${oslId}`);
    process.exit(1);
  }

  console.log(`INFO: Fetching color record for id ${oslRecord.color_id}...`);
  const { data: colorData, error: colorError } = await supabase
    .from("t_colors")
    .select("*")
    .eq("id", oslRecord.color_id)
    .single();
  if (colorError) {
    const msg = `Error fetching color: ${JSON.stringify(colorError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Fetching color for OSL ${oslId}`);
    process.exit(1);
  }


// 5) Fetch inventory from t_inv_osl
console.log(`INFO: Fetching inventory from t_inv_osl for OSL id ${oslId}...`);
let { data: invData, error: invError } = await supabase
  .from("t_inv_osl")
  .select("available_quantity")
  .eq("id", oslId)
  .maybeSingle();

if (invError) {
  const msg = `Error fetching inventory: ${JSON.stringify(invError)}`;
  console.error("ERROR:", msg);
  await logError(msg, `Fetching inventory for OSL ${oslId}`);
  process.exit(1);
}

if (!invData) {
  console.log(`INFO: No inventory record found for OSL id ${oslId}. Inserting new record with available_quantity = 0.`);
  const { data: newInv, error: newInvError } = await supabase
    .from("t_inv_osl")
    .insert([{ id: oslId, available_quantity: 0 }]);
  if (newInvError) {
    const msg = `Error inserting new inventory record for OSL ${oslId}: ${JSON.stringify(newInvError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Inserting inventory for OSL ${oslId}`);
    process.exit(1);
  }
  invData = { available_quantity: 0 };
}



  // 6) Fetch configuration for public image server and folder
  console.log("INFO: Fetching public image server and folder from t_config...");
  const { data: configData, error: configError } = await supabase
    .from("t_config")
    .select("key, value")
    .in("key", ["public_image_server", "folder_mps"]);
  if (configError) {
    const msg = `Error fetching t_config: ${JSON.stringify(configError)}`;
    console.error("ERROR:", msg);
    await logError(msg, `Fetching t_config for OSL ${oslId}`);
    process.exit(1);
  }
  const publicImageServerRow = configData.find(row => row.key === "public_image_server");
  const folderMpsRow = configData.find(row => row.key === "folder_mps");
  if (!publicImageServerRow || !folderMpsRow) {
    const msg = "Missing configuration for public_image_server or folder_mps";
    console.error("ERROR:", msg);
    await logError(msg, `Fetching t_config for OSL ${oslId}`);
    process.exit(1);
  }
  const imageSrc = `${publicImageServerRow.value}/${folderMpsRow.value}/${mpsRecord.id}.jpg`;

  // 7) Construct handle from brand, plastic, mold, stamp
  function generateMPSHandle(brand, plastic, mold, stamp) {
    let base = `${brand}-${plastic}-${mold}-${stamp}`.toLowerCase();
    base = base
      .replace(/ /g, "-")
      .replace(/'/g, "")
      .replace(/\//g, "")
      .replace(/\./g, "-")
      .replace(/&/g, "-")
      .replace(/\(/g, "")
      .replace(/\)/g, "")
      .replace(/"/g, "")
      .replace(/%/g, "")
      .replace(/#/g, "")
      .replace(/-\$/g, "");
    while (base.includes("--")) {
      base = base.replace(/--/g, "-");
    }
    return base;
  }

  const handle = generateMPSHandle(
    brandData.brand,
    plasticData.plastic,
    moldData.mold,
    stampData.stamp
  );

  // 8) Construct product title
  function safeLower(val) {
    return val ? val.toLowerCase() : "";
  }
  let prodTitle = `${brandData.brand} ${plasticData.plastic} ${moldData.mold} ${moldData.type}`;
  if (stampData.stamp && safeLower(stampData.stamp) === "stock") {
    prodTitle += " (Stock Stamp)";
  } else if (stampData.stamp) {
    prodTitle += ` with ${stampData.stamp} Stamp`;
  }
  prodTitle = prodTitle.replace("Stamp Stamp", "Stamp") + " [Back Stock Inventory - Disc and Stamp Color WILL VARY]";

  // 9) Construct tags
  const discountFlag = mpsRecord["30_percent_discount_allowed"] ? "Y" : "N";

  // Calculate weight range tags for OSL products
  function calculateWeightRangeTag(weight) {
    const roundedWeight = Math.round(weight * 2) / 2;
    if (roundedWeight >= 10 && roundedWeight <= 49.5) return 'wt_rng_10-49';
    else if (roundedWeight >= 50 && roundedWeight <= 99.5) return 'wt_rng_50-99';
    else if (roundedWeight >= 100 && roundedWeight <= 119.5) return 'wt_rng_100-119';
    else if (roundedWeight >= 120 && roundedWeight <= 139.5) return 'wt_rng_120-139';
    else if (roundedWeight >= 140 && roundedWeight <= 149.5) return 'wt_rng_140-149';
    else if (roundedWeight >= 150 && roundedWeight <= 159.5) return 'wt_rng_150-159';
    else if (roundedWeight >= 160 && roundedWeight <= 169.5) return 'wt_rng_160-169';
    else if (roundedWeight >= 170 && roundedWeight <= 174.5) return 'wt_rng_170-174';
    else if (roundedWeight >= 175 && roundedWeight <= 180.5) return 'wt_rng_175-180';
    else if (roundedWeight >= 181 && roundedWeight <= 200) return 'wt_rng_181-200';
    else if (roundedWeight >= 201 && roundedWeight <= 249) return 'wt_rng_201-249';
    else return null;
  }

  // For OSL products, add all weight range tags that overlap with the OSL's weight range
  const weightRangeTags = [];
  const minWeight = oslRecord.min_weight;
  const maxWeight = oslRecord.max_weight;

  // Check each possible weight range to see if it overlaps with the OSL range
  const possibleRanges = [
    { min: 10, max: 49.5, tag: 'wt_rng_10-49' },
    { min: 50, max: 99.5, tag: 'wt_rng_50-99' },
    { min: 100, max: 119.5, tag: 'wt_rng_100-119' },
    { min: 120, max: 139.5, tag: 'wt_rng_120-139' },
    { min: 140, max: 149.5, tag: 'wt_rng_140-149' },
    { min: 150, max: 159.5, tag: 'wt_rng_150-159' },
    { min: 160, max: 169.5, tag: 'wt_rng_160-169' },
    { min: 170, max: 174.5, tag: 'wt_rng_170-174' },
    { min: 175, max: 180.5, tag: 'wt_rng_175-180' },
    { min: 181, max: 200, tag: 'wt_rng_181-200' },
    { min: 201, max: 249, tag: 'wt_rng_201-249' }
  ];

  for (const range of possibleRanges) {
    // Check if ranges overlap: OSL range overlaps with weight range if max of one >= min of other
    if (maxWeight >= range.min && minWeight <= range.max) {
      weightRangeTags.push(range.tag);
    }
  }

  console.log(`INFO: OSL weight range ${minWeight}g-${maxWeight}g → Weight range tags: ${weightRangeTags.join(', ')}`);

  let tags = `class_os,disc_type_${moldData.type},disc_plastic_${plasticData.plastic},disc_mold_${moldData.mold},disc_brand_${brandData.brand},30ok_${discountFlag},disc_stamp_${stampData.stamp},DiscFN_Speed_${moldData.speed},DiscFN_Glide_${moldData.glide},DiscFN_Turn_${moldData.turn},DiscFN_Fade_${moldData.fade}`;

  // Add weight range tags if any were calculated
  if (weightRangeTags.length > 0) {
    tags += `,${weightRangeTags.join(',')}`;
  }

  // Add player tag from stamp if available
  if (stampPlayerData) {
    tags += `,player_${stampPlayerData.name}`;
    console.log(`INFO: Added player tag from stamp: player_${stampPlayerData.name}`);
  } else {
    console.log("INFO: No player data from stamp; skipping stamp player tags.");
  }

  // 10) Options
  const option1Value = `${oslRecord.min_weight}g-${oslRecord.max_weight}g`;
  const option2Value = moldData.mold?.trim() || "Unknown Mold";
  const option3Value = colorData.color?.trim() || "Unknown Color";

  // 11) Variant details
  const variantSKU = "OS" + oslRecord.id;
  const variantBarcode = "'" + (oslRecord.upc || "");
  // Convert max weight from grams to pounds with 2 decimal places
  // Add 5 grams for packaging, then convert to pounds (1 gram = 0.00220462 pounds)
  const variantWeight = Math.round((oslRecord.max_weight + 5) * 0.00220462 * 100) / 100;
  const variantPrice = mpsRecord.val_override_retail_price != null
    ? mpsRecord.val_override_retail_price
    : plasticData.val_retail_price;
  const variantCompareAtPrice = mpsRecord.val_override_msrp != null
    ? mpsRecord.val_override_msrp
    : plasticData.val_msrp;
  const onlineReleaseDate = new Date(mpsRecord.release_date_online);
  const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  const variantInventoryQty = (onlineReleaseDate > sevenDaysAgo)
    ? 0
    : (invData.available_quantity || 0);

  // 12) Image alt text
  const imageAltText = `${brandData.brand} ${plasticData.plastic} ${moldData.mold}` +
    ((stampData.stamp && safeLower(stampData.stamp) === "stock")
      ? ""
      : ` with ${stampData.stamp} Stamp`);

  // Final product data
  const productData = {
    orderSheetLineId: oslRecord.id,
    title: prodTitle,
    vendor: brandData.brand,
    tags,
    option1Value,
    option2Value,
    option3Value,
    variantSKU,
    variantBarcode,
    variantWeight,
    variantPrice,
    variantCompareAtPrice,
    variantInventoryQty,
    imageSrc,
    imageAltText,
    handle
  };

  console.log("INFO: Final product data:\n", JSON.stringify(productData, null, 2));

  try {
    // --- Use GraphQL to see if a product by this handle already exists ---
    console.log("DEBUG: Searching productByHandle with handle:", productData.handle);
    const existingProductGID = await getProductGIDByHandle(productData.handle);
    console.log("DEBUG: Resulting existingProductGID:", existingProductGID);

    let variant = null; // Declare variant variable in broader scope

    if (existingProductGID) {
      // Parse the numeric ID for REST usage
      const match = existingProductGID.match(/Product\/(\d+)/);
      const productId = match ? match[1] : null;

      if (!productId) {
        throw new Error(`Could not parse numeric productId from GID: ${existingProductGID}`);
      }

      console.log(`INFO: Product found (GID ${existingProductGID}), numeric ID ${productId}. Checking existing product options...`);

      // Get the existing product details to check option structure
      const existingProduct = await getProductDetailsById(productId);
      console.log("DEBUG: Existing product options:", JSON.stringify(existingProduct.options, null, 2));

      // Validate existing product option structure for OS products
      if (existingProduct.options.length === 3) {
        // Correct 3-option structure: Weight Range, Mold, Color
        console.log("INFO: Existing product uses correct 3-option structure (Weight Range, Mold, Color). Adding variant...");

        // Validate the existing options match what we expect for 3-option structure
        const option1Valid = existingProduct.options[0].name === "Weight Range" && existingProduct.options[0].position === 1;
        const option2Valid = existingProduct.options[1].name === "Mold" && existingProduct.options[1].position === 2;
        const option3Valid = existingProduct.options[2].name === "Color" && existingProduct.options[2].position === 3;

        if (!option1Valid || !option2Valid || !option3Valid) {
          const errorMsg = `Existing product has unexpected 3-option structure: Option 1: '${existingProduct.options[0].name}' (pos ${existingProduct.options[0].position}), Option 2: '${existingProduct.options[1].name}' (pos ${existingProduct.options[1].position}), Option 3: '${existingProduct.options[2].name}' (pos ${existingProduct.options[2].position}). Expected: Weight Range, Mold, Color.`;
          console.error("ERROR:", errorMsg);
          throw new Error(errorMsg);
        }

        // Product structure is correct, proceed with adding variant

      } else {
        // Incorrect option structure for OS products
        let optionDetails = existingProduct.options.map((opt, index) =>
          `Option ${index + 1}: '${opt.name}' (pos ${opt.position})`
        ).join(', ');

        const errorMsg = `MANUAL FIX REQUIRED: Existing OS product has incorrect ${existingProduct.options.length}-option structure (${optionDetails}). OS products must have exactly 3 options: Weight Range, Mold, Color. Please manually recreate this product in Shopify with the correct 3-option structure, then retry publishing this OSL.`;
        console.error("ERROR:", errorMsg);
        throw new Error(errorMsg);
      }

      console.log("INFO: Option structure validated. Adding new variant...");

      // Create the new variant using the standard product data (always 3 options for OS products)
      variant = await addVariantToProduct(productData, productId);
      console.log("INFO: Successfully added variant:", variant);

      // Only attempt image association if this is a newly created variant (not an existing one)
      if (variant.id !== 'existing') {
        // Use the existing product details we already fetched
        if (existingProduct.images && existingProduct.images.length > 0) {
          const parentImageId = existingProduct.images[0].id;
          await updateVariantImage(variant.id, parentImageId);
          console.log("INFO: Successfully associated parent's image with the new variant.");
        } else {
          console.log("INFO: Parent product has no image; variant will have no associated image.");
        }
      } else {
        console.log("INFO: Variant already existed - skipping image association.");
      }
    } else {
      // Product not found; create a new product
      console.log(`INFO: Product with handle "${productData.handle}" not found. Creating new product...`);
      const product = await createShopifyProduct(productData);
      console.log("INFO: Successfully created new product:", product);
    }

    // Create appropriate success message based on whether variant was created or already existed
    let successNote;
    if (existingProductGID && variant && variant.id === 'existing') {
      successNote = "Success! Shopify product variant already existed - no action needed. OSL processed via publishProductOSL.js.";
    } else if (existingProductGID) {
      successNote = "Success! New Shopify product variant created and added to existing product via publishProductOSL.js.";
    } else {
      successNote = "Success! New Shopify product created with variant via publishProductOSL.js.";
    }
    console.log("INFO: Updating t_order_sheet_lines with success note...");

    // Instead of updating the record directly, create a task in the task queue
    // to update the record, which will be processed by the worker
    console.log("INFO: Creating a task to update the OSL record...");

    const { error: taskError } = await supabase
      .from("t_task_queue")
      .insert({
        task_type: "update_osl_after_publish",
        payload: {
          id: oslId,
          shopify_uploaded_at: new Date().toISOString(),
          shopify_product_uploaded_notes: successNote
        },
        status: "pending",
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      });

    if (taskError) {
      const msg = `Failed to create update task for OSL id ${oslRecord.id}: ${taskError.message}`;
      console.error("ERROR:", msg);
      await logError(msg, `Creating update task for OSL ${oslId}`);
      process.exit(1);
    }

    console.log(`INFO: Successfully created task to update OSL id ${oslRecord.id}.`);
    console.log(`INFO: Updated t_order_sheet_lines for OSL id ${oslRecord.id}.`);
  } catch (err) {
    // Log the original error message for worker daemon extraction
    console.error("ERROR:", err.message);

    // Also log a more detailed message for debugging
    const detailedMsg = `Failed to process Shopify product for OSL id ${oslRecord.id}: ${err.message}`;
    await logError(detailedMsg, `Processing Shopify product for OSL ${oslId}`);

    // Store the original error message in the database
    const { error: noteError } = await supabase
      .from("t_order_sheet_lines")
      .update({ shopify_product_uploaded_notes: err.message })
      .eq("id", oslId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_product_uploaded_notes for OSL id ${oslRecord.id}: ${noteError.message}`);
      await logError(noteError.message, `Updating shopify_product_uploaded_notes for OSL ${oslId}`);
    }
    process.exit(1);
  }

  console.log("INFO: publishProductOSL.js process completed successfully.");
}

main().catch(async err => {
  console.error("ERROR: Unexpected error:", err);
  await logError(err.message, "Unexpected error in main function of publishProductOSL.js");
  process.exit(1);
});
